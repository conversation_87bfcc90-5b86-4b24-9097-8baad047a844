// Variables For Transparent Layout
$bg-hibiscus: linear-gradient(to right bottom, #f05f57, #c83d5c, #99245a, #671351, #360940);
$bg-purple-pizzazz: linear-gradient(to right bottom, #662d86, #8b2a8a, #ae2389, #cf1d83, #ed1e79);
$bg-blue-lagoon: linear-gradient(to right bottom, #144e68, #006d83, #008d92, #00ad91, #57ca85);
$bg-electric-violet: linear-gradient(to left top, #4a00e0, #600de0, #7119e1, #8023e1, #8e2de2);
$bg-portage: linear-gradient(to left top, #97abff, #798ce5, #5b6ecb, #3b51b1, #123597);
$bg-tundora: linear-gradient(to left top, #474747, #4a4a4a, #4c4d4d, #4f5050, #525352);

.customizer {
  width: 400px;
  right: -400px;
  padding: 0;
  background-color: #fff;
  z-index: 1051;
  position: fixed;
  top: 0;
  bottom: 0;
  height: 100vh;
  transition: right 0.4s cubic-bezier(0.05, 0.74, 0.2, 0.99);
  backface-visibility: hidden;
  border-left: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);

  &.open {
    right: 0;
  }

  .customizer-content {
    position: relative;
    height: 100%;
    overflow-y: auto;
  }

  a.customizer-toggle {
    background: #fff;
    color: var(--cui-primary);
    display: block;
    box-shadow: -3px 0px 8px rgba(0, 0, 0, 0.1);
  }

  a.customizer-close {
    color: #000;
  }

  .customizer-close {
    position: absolute;
    right: 10px;
    top: 10px;
    padding: 7px;
    width: auto;
    z-index: 10;
    cursor: pointer;
  }

  .customizer-toggle {
    position: absolute;
    top: 35%;
    width: 54px;
    height: 50px;
    left: -54px;
    text-align: center;
    line-height: 50px;
    cursor: pointer;
  }

  .color-options {
    a {
      white-space: pre;
    }
  }

  .cz-bg-color {
    margin: 0 auto;

    span {
      &:hover {
        cursor: pointer;
      }

      &.white {
        color: #ddd !important;
      }
    }
  }

  .cz-bg-color,
  .cz-tl-bg-color {
    .selected {
      box-shadow: 0 0 10px 3px var(--cui-primary);
      border: 3px solid #fff;
    }
  }

  .cz-bg-image {
    &:hover {
      cursor: pointer;
    }

    img.rounded {
      border-radius: 1rem !important;
      border: 2px solid #e6e6e6;
      height: 100px;
      width: 50px;

      &.selected {
        border: 2px solid var(--cui-success);
      }
    }
  }

  .tl-color-options {
    display: none;
  }

  .cz-tl-bg-image {
    img.rounded {
      border-radius: 1rem !important;
      border: 2px solid #e6e6e6;
      height: 100px;
      width: 70px;

      &.selected {
        border: 2px solid var(--cui-success);
      }

      &:hover {
        cursor: pointer;
      }
    }
  }

  // Background gradient classes
  .bg-hibiscus {
    background-image: $bg-hibiscus;
    background-size: cover;
    background-size: 100% 100%;
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    transition: background 0.3s;
  }

  .bg-purple-pizzazz {
    background-image: $bg-purple-pizzazz;
    background-size: cover;
    background-size: 100% 100%;
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    transition: background 0.3s;
  }

  .bg-blue-lagoon {
    background-image: $bg-blue-lagoon;
    background-size: cover;
    background-size: 100% 100%;
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    transition: background 0.3s;
  }

  .bg-electric-violet {
    background-image: $bg-electric-violet;
    background-size: cover;
    background-size: 100% 100%;
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    transition: background 0.3s;
  }

  .bg-portage {
    background-image: $bg-portage;
    background-size: cover;
    background-size: 100% 100%;
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    transition: background 0.3s;
  }

  .bg-tundora {
    background-image: $bg-tundora;
    background-size: cover;
    background-size: 100% 100%;
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    transition: background 0.3s;
  }

  .cz-bg-color,
  .cz-tl-bg-color {
    .col {
      span.rounded-circle {
        &:hover {
          cursor: pointer;
        }
      }
    }
  }

  // Toggle switches
  .togglebutton {
    .switch {
      .custom-control {
        .custom-control-input {
          &:checked ~ .custom-control-label::before {
            background-color: var(--cui-primary);
            border-color: var(--cui-primary);
          }
        }
      }
    }
  }

  // Form controls
  .custom-select {
    border: 1px solid #ced4da;
    border-radius: 0.25rem;

    &:focus {
      border-color: var(--cui-primary);
      box-shadow: 0 0 0 0.2rem rgba(var(--cui-primary-rgb), 0.25);
    }
  }

  // Layout options
  .layout-switch {
    .custom-control-inline {
      margin-right: 1rem;
    }
  }
}

// RTL Support
[dir="rtl"] :host ::ng-deep {
  .customizer {
    left: -400px;
    right: auto;
    border-right: 1px solid rgba(0, 0, 0, 0.05);
    border-left: 0px;

    &.open {
      left: 0;
      right: auto;
    }

    .customizer-close {
      left: 10px;
      right: auto;
    }

    .customizer-toggle {
      right: -54px;
      left: auto;
    }
  }
}

// Responsive styles
@media (max-width: 768px) {
  .customizer {
    display: none !important;
  }
}

// Gradient color classes for sidebar colors
.gradient-pomegranate {
  background: linear-gradient(45deg, #c0392b, #e74c3c);
}

.gradient-king-yna {
  background: linear-gradient(45deg, #f39c12, #f1c40f);
}

.gradient-ibiza-sunset {
  background: linear-gradient(45deg, #e67e22, #f39c12);
}

.gradient-flickr {
  background: linear-gradient(45deg, #e91e63, #9c27b0);
}

.gradient-purple-bliss {
  background: linear-gradient(45deg, #9b59b6, #8e44ad);
}

.gradient-man-of-steel {
  background: linear-gradient(45deg, #34495e, #2c3e50);
}

.gradient-purple-love {
  background: linear-gradient(45deg, #8e44ad, #9b59b6);
}