/**
 * Theme configuration interfaces for the customizer component
 * Based on zen-logistic implementation with Angular best practices
 */

export interface TemplateConfig {
  layout: {
    variant: string; // 'Light', 'Dark', 'Transparent'
    dir: string; // 'ltr', 'rtl'
    customizer: {
      hidden: boolean;
    };
    sidebar: {
      collapsed: boolean;
      size: string; // 'sidebar-lg', 'sidebar-md', 'sidebar-sm'
      backgroundColor: string;
      backgroundImage: boolean;
      backgroundImageURL: string;
    };
  };
}

export interface CustomizerOptions {
  direction: string;
  bgColor: string;
  transparentColor: string;
  bgImage: string;
  bgImageDisplay: boolean;
  compactMenu: boolean;
  sidebarSize: string;
  layout: string; // 'Light', 'Dark', 'Transparent'
}

export interface ThemeColorData {
  color: string;
}

export interface ThemeUpdateResponse {
  id: string | number;
  color: string;
}

/**
 * Available sidebar background colors
 */
export const SIDEBAR_COLORS = {
  BLACK: 'black',
  POMEGRANATE: 'pomegranate',
  KING_YNA: 'king-yna',
  IBIZA_SUNSET: 'ibiza-sunset',
  FLICKR: 'flickr',
  PURPLE_BLISS: 'purple-bliss',
  MAN_OF_STEEL: 'man-of-steel',
  PURPLE_LOVE: 'purple-love',
  PRIMARY: 'primary',
  WHITE: 'white'
} as const;

/**
 * Available transparent background colors
 */
export const TRANSPARENT_COLORS = {
  BG_GLASS_1: 'bg-glass-1',
  BG_GLASS_2: 'bg-glass-2',
  BG_GLASS_3: 'bg-glass-3',
  BG_GLASS_4: 'bg-glass-4',
  BG_HIBISCUS: 'bg-hibiscus',
  BG_PURPLE_PIZZAZ: 'bg-purple-pizzaz',
  BG_BLUE_LAGOON: 'bg-blue-lagoon',
  BG_ELECTRIC_VIOLET: 'bg-electric-viloet',
  BG_PROTAGE: 'bg-protage',
  BG_TUNDORA: 'bg-tundora'
} as const;

/**
 * Available sidebar sizes
 */
export const SIDEBAR_SIZES = {
  LARGE: 'sidebar-lg',
  MEDIUM: 'sidebar-md',
  SMALL: 'sidebar-sm'
} as const;

/**
 * Available layout variants
 */
export const LAYOUT_VARIANTS = {
  LIGHT: 'Light',
  DARK: 'Dark',
  TRANSPARENT: 'Transparent'
} as const;

export type SidebarColor = typeof SIDEBAR_COLORS[keyof typeof SIDEBAR_COLORS];
export type TransparentColor = typeof TRANSPARENT_COLORS[keyof typeof TRANSPARENT_COLORS];
export type SidebarSize = typeof SIDEBAR_SIZES[keyof typeof SIDEBAR_SIZES];
export type LayoutVariant = typeof LAYOUT_VARIANTS[keyof typeof LAYOUT_VARIANTS];
