<!--Theme customizer Starts-->
<div #customizer class="customizer border-left-blue-grey border-left-lighten-4 d-none d-sm-none d-md-block" [ngClass]="{open: isOpen}">
  <a class="customizer-close" (click)="closeCustomizer()">
    <svg cIcon name="cilX" size="lg"></svg>
  </a>
  <a class="customizer-toggle bg-danger d-none" id="customizer-toggle-icon" (click)="toggleCustomizer()">
    <svg cIcon name="cilSettings" size="lg" class="fa-spin"></svg>
  </a>
  <div class="customizer-content p-3 ps-container ps-theme-dark text-left">
    <h4 class="text-uppercase mb-0 text-bold-400">Theme Customizer</h4>
    <p>Customize &amp; Preview in Real Time</p>
    <hr>

    <!-- Layout Options-->
    <h6 class="text-center text-bold-500 mb-3 text-uppercase">Layout Options</h6>
    <div class="layout-switch ml-4">
      <div class="custom-control custom-radio d-inline-block custom-control-inline light-layout">
        <input class="custom-control-input" id="ll-switch" type="radio" name="layout-switch"
               [checked]="config.layout.variant === LAYOUT_VARIANTS.LIGHT" (click)="onLightLayout()" />
        <label class="custom-control-label" for="ll-switch">Light</label>
      </div>
      <div class="custom-control custom-radio d-inline-block custom-control-inline dark-layout">
        <input class="custom-control-input" id="dl-switch" type="radio" name="layout-switch"
               [checked]="config.layout.variant === LAYOUT_VARIANTS.DARK" (click)="onDarkLayout()" />
        <label class="custom-control-label" for="dl-switch">Dark</label>
      </div>
      <div class="custom-control custom-radio d-inline-block custom-control-inline transparent-layout">
        <input class="custom-control-input" id="tl-switch" type="radio" name="layout-switch"
               [checked]="config.layout.variant === LAYOUT_VARIANTS.TRANSPARENT" (click)="onTransparentLayout()" />
        <label class="custom-control-label" for="tl-switch">Transparent</label>
      </div>
    </div>
    <hr />

    <!-- Sidebar Options Starts-->
    <h6 class="text-center text-bold-500 mb-3 text-uppercase sb-options">Sidebar Color Options</h6>
    <div class="cz-bg-color sb-color-options">
      <div class="row p-1">
        <div class="col"><span class="gradient-pomegranate d-block rounded-circle" style="width:20px; height:20px;"
            data-bg-color="pomegranate" [ngClass]="{'selected': selectedBgColor === SIDEBAR_COLORS.POMEGRANATE}"
            (click)="changeSidebarBgColor(SIDEBAR_COLORS.POMEGRANATE)"></span></div>
        <div class="col"><span class="gradient-king-yna d-block rounded-circle" style="width:20px; height:20px;"
            data-bg-color="king-yna" [ngClass]="{'selected': selectedBgColor === SIDEBAR_COLORS.KING_YNA}"
            (click)="changeSidebarBgColor(SIDEBAR_COLORS.KING_YNA)"></span></div>
        <div class="col"><span class="gradient-ibiza-sunset d-block rounded-circle" style="width:20px; height:20px;"
            data-bg-color="ibiza-sunset" [ngClass]="{'selected': selectedBgColor === SIDEBAR_COLORS.IBIZA_SUNSET}"
            (click)="changeSidebarBgColor(SIDEBAR_COLORS.IBIZA_SUNSET)"></span></div>
        <div class="col"><span class="gradient-flickr d-block rounded-circle" style="width:20px; height:20px;"
            data-bg-color="flickr" [ngClass]="{'selected': selectedBgColor === SIDEBAR_COLORS.FLICKR}"
            (click)="changeSidebarBgColor(SIDEBAR_COLORS.FLICKR)"></span></div>
        <div class="col"><span class="gradient-purple-bliss d-block rounded-circle" style="width:20px; height:20px;"
            data-bg-color="purple-bliss" [ngClass]="{'selected': selectedBgColor === SIDEBAR_COLORS.PURPLE_BLISS}"
            (click)="changeSidebarBgColor(SIDEBAR_COLORS.PURPLE_BLISS)"></span></div>
        <div class="col"><span class="gradient-man-of-steel d-block rounded-circle" style="width:20px; height:20px;"
            data-bg-color="man-of-steel" [ngClass]="{'selected': selectedBgColor === SIDEBAR_COLORS.MAN_OF_STEEL}"
            (click)="changeSidebarBgColor(SIDEBAR_COLORS.MAN_OF_STEEL)"></span></div>
        <div class="col"><span class="gradient-purple-love d-block rounded-circle" style="width:20px; height:20px;"
            data-bg-color="purple-love" [ngClass]="{'selected': selectedBgColor === SIDEBAR_COLORS.PURPLE_LOVE}"
            (click)="changeSidebarBgColor(SIDEBAR_COLORS.PURPLE_LOVE)"></span></div>
      </div>
      <div class="row p-1">
        <div class="col"><span class="bg-black d-block rounded-circle" style="width:20px; height:20px;"
            data-bg-color="black" [ngClass]="{'selected': selectedBgColor === SIDEBAR_COLORS.BLACK}"
            (click)="changeSidebarBgColor(SIDEBAR_COLORS.BLACK)"></span></div>
        <div class="col"><span class="bg-grey d-block rounded-circle" style="width:20px; height:20px;"
            data-bg-color="white" [ngClass]="{'selected': selectedBgColor === SIDEBAR_COLORS.WHITE}"
            (click)="changeSidebarBgColor(SIDEBAR_COLORS.WHITE)"></span></div>
        <div class="col"><span class="bg-primary d-block rounded-circle" style="width:20px; height:20px;"
            data-bg-color="primary" [ngClass]="{'selected': selectedBgColor === SIDEBAR_COLORS.PRIMARY}"
            (click)="changeSidebarBgColor(SIDEBAR_COLORS.PRIMARY)"></span></div>
      </div>
    </div>
    <!-- Sidebar Options Ends-->

    <!-- Transparent Layout Bg color Options-->
    <h6 class="text-center text-bold-500 mb-3 text-uppercase tl-color-options d-none">Background Colors</h6>
    <div class="cz-tl-bg-color d-none">
      <div class="row p-1">
        <div class="col"><span class="bg-hibiscus d-block rounded-circle" style="width:20px; height:20px;"
            data-bg-color="bg-hibiscus" [ngClass]="{'selected': selectedTLBgColor === TRANSPARENT_COLORS.BG_HIBISCUS}"
            (click)="changeSidebarTLBgColor(TRANSPARENT_COLORS.BG_HIBISCUS)"></span></div>
        <div class="col"><span class="bg-purple-pizzazz d-block rounded-circle" style="width:20px; height:20px;"
            data-bg-color="bg-purple-pizzazz" [ngClass]="{'selected': selectedTLBgColor === TRANSPARENT_COLORS.BG_PURPLE_PIZZAZ}"
            (click)="changeSidebarTLBgColor(TRANSPARENT_COLORS.BG_PURPLE_PIZZAZ)"></span></div>
        <div class="col"><span class="bg-blue-lagoon d-block rounded-circle" style="width:20px; height:20px;"
            data-bg-color="bg-blue-lagoon" [ngClass]="{'selected': selectedTLBgColor === TRANSPARENT_COLORS.BG_BLUE_LAGOON}"
            (click)="changeSidebarTLBgColor(TRANSPARENT_COLORS.BG_BLUE_LAGOON)"></span></div>
        <div class="col"><span class="bg-electric-violet d-block rounded-circle" style="width:20px; height:20px;"
            data-bg-color="bg-electric-violet" [ngClass]="{'selected': selectedTLBgColor === TRANSPARENT_COLORS.BG_ELECTRIC_VIOLET}"
            (click)="changeSidebarTLBgColor(TRANSPARENT_COLORS.BG_ELECTRIC_VIOLET)"></span></div>
        <div class="col"><span class="bg-portage d-block rounded-circle" style="width:20px; height:20px;"
            data-bg-color="bg-portage" [ngClass]="{'selected': selectedTLBgColor === TRANSPARENT_COLORS.BG_PROTAGE}"
            (click)="changeSidebarTLBgColor(TRANSPARENT_COLORS.BG_PROTAGE)"></span></div>
        <div class="col"><span class="bg-tundora d-block rounded-circle" style="width:20px; height:20px;"
            data-bg-color="bg-tundora" [ngClass]="{'selected': selectedTLBgColor === TRANSPARENT_COLORS.BG_TUNDORA}"
            (click)="changeSidebarTLBgColor(TRANSPARENT_COLORS.BG_TUNDORA)"></span></div>
      </div>
    </div>
    <!-- Transparent Layout Bg color Ends-->
    <hr />

    <!--Sidebar BG Image Toggle Starts-->
    <div class="togglebutton toggle-sb-bg-img">
      <div class="switch switch border-0 d-flex justify-content-between w-100">
        <span>Sidebar Bg Image</span>
        <div class="float-right">
          <div class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0">
            <input type="checkbox" class="custom-control-input cz-bg-image-display"
                   [checked]="isBgImageDisplay" id="sidebar-bg-img" (change)="bgImageDisplay($event)">
            <label class="custom-control-label d-block" for="sidebar-bg-img"></label>
          </div>
        </div>
      </div>
      <hr>
    </div>
    <!--Sidebar BG Image Toggle Ends-->

    <!--Compact Menu Starts-->
    <div class="togglebutton">
      <div class="switch switch border-0 d-flex justify-content-between w-100">
        <span>Compact Menu</span>
        <div class="float-right">
          <div class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0">
            <input type="checkbox" [checked]="config.layout.sidebar.collapsed"
                   class="custom-control-input cz-compact-menu" id="cz-compact-menu" (change)="toggleCompactMenu($event)">
            <label class="custom-control-label d-block" for="cz-compact-menu"></label>
          </div>
        </div>
      </div>
    </div>
    <!--Compact Menu Ends-->
    <hr>

    <!--Sidebar Width Starts-->
    <div>
      <label for="cz-sidebar-width">Sidebar Width</label>
      <select id="cz-sidebar-width" #width class="custom-select cz-sidebar-width float-right" (change)="changeSidebarWidth(width.value)">
        <option value="sidebar-sm" [selected]="size === 'sidebar-sm'">Small</option>
        <option value="sidebar-md" [selected]="size === 'sidebar-md'">Medium</option>
        <option value="sidebar-lg" [selected]="size === 'sidebar-lg'">Large</option>
      </select>
    </div>
    <!--Sidebar Width Ends-->
  </div>
</div>
<!--Theme customizer Ends-->
