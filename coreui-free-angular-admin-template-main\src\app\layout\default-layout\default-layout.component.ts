import { Component, OnInit, OnDestroy } from '@angular/core';
import { RouterLink, RouterOutlet } from '@angular/router';
import { NgScrollbar } from 'ngx-scrollbar';
import { Subscription } from 'rxjs';

import { IconDirective } from '@coreui/icons-angular';
import {
  ContainerComponent,
  ShadowOnScrollDirective,
  SidebarBrandComponent,
  SidebarComponent,
  SidebarFooterComponent,
  SidebarHeaderComponent,
  SidebarNavComponent,
  SidebarToggleDirective,
  SidebarTogglerDirective
} from '@coreui/angular';

import { DefaultFooterComponent, DefaultHeaderComponent } from './';
import { navItems } from './_nav';
import { CommonModule } from '@angular/common';
import { CustomizerThemeComponent } from './customizer-theme/customizer-theme.component';
import { LayoutService } from '../../../services';
import { CustomizerOptions } from '../../../models';

function isOverflown(element: HTMLElement) {
  return (
    element.scrollHeight > element.clientHeight ||
    element.scrollWidth > element.clientWidth
  );
}

@Component({
  selector: 'app-dashboard',
  templateUrl: './default-layout.component.html',
  styleUrls: ['./default-layout.component.scss'],
  imports: [
    SidebarComponent,
    SidebarHeaderComponent,
    SidebarBrandComponent,
    SidebarNavComponent,
    SidebarFooterComponent,
    SidebarToggleDirective,
    SidebarTogglerDirective,
    ContainerComponent,
    DefaultFooterComponent,
    DefaultHeaderComponent,
    IconDirective,
    NgScrollbar,
    RouterOutlet,
    RouterLink,
    ShadowOnScrollDirective,
    CommonModule,
    CustomizerThemeComponent
  ]
})
export class DefaultLayoutComponent implements OnInit, OnDestroy {
  public navItems = [...navItems];
  private layoutSubscription: Subscription = new Subscription();

  constructor(private layoutService: LayoutService) {}

  ngOnInit(): void {
    // Subscribe to customizer changes to apply theme modifications
    this.layoutSubscription.add(
      this.layoutService.customizerChangeEmitted$.subscribe((options: CustomizerOptions) => {
        if (options) {
          this.applyThemeChanges(options);
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.layoutSubscription.unsubscribe();
  }

  toggleCustomizer(): void {
    this.layoutService.emitCustomizerChange('toggle');
  }

  private applyThemeChanges(options: CustomizerOptions): void {
    const body = document.body;
    const sidebar = document.getElementById('sidebar1');

    if (sidebar) {
      // Apply sidebar background color
      if (options.bgColor) {
        sidebar.setAttribute('data-coreui-theme', options.bgColor);
      }

      // Apply sidebar size
      if (options.sidebarSize) {
        body.classList.remove('sidebar-sm', 'sidebar-md', 'sidebar-lg');
        body.classList.add(options.sidebarSize);
      }

      // Apply compact menu
      if (options.compactMenu !== undefined) {
        if (options.compactMenu) {
          sidebar.classList.add('sidebar-narrow');
        } else {
          sidebar.classList.remove('sidebar-narrow');
        }
      }

      // Apply layout variant
      if (options.layout) {
        body.classList.remove('layout-light', 'layout-dark', 'layout-transparent');
        body.classList.add(`layout-${options.layout.toLowerCase()}`);
      }
    }
  }
}
